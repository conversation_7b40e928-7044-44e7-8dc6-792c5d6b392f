<?php

declare(strict_types=1);

namespace App\Modules\Order\Data;

use App\Data\Casts\MoneyCast;
use App\Data\Transformers\MoneyTransformer;
use App\Enums\ProductStockStatus;
use Brick\Money\Money;
use Spatie\LaravelData\Attributes\WithCast;
use Spatie\LaravelData\Attributes\WithTransformer;
use Spatie\LaravelData\Data;

final class ProductOfferData extends Data
{
    public function __construct(
        public readonly string $id,
        public readonly string $vendorSku,
        public readonly ?string $unit_of_measure,
        #[WithTransformer(MoneyTransformer::class)]
        #[WithCast(MoneyCast::class)]
        public readonly ?Money $price,
        public readonly ProductStockStatus $stockStatus,
        public readonly int $increments,
        public readonly VendorData $vendor,
    ) {}
}
