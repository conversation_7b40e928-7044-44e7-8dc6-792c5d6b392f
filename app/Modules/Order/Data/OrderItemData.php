<?php

declare(strict_types=1);

namespace App\Modules\Order\Data;

use App\Data\Casts\MoneyCast;
use App\Data\Transformers\MoneyTransformer;
use App\Enums\OrderItemStatus;
use App\Models\OrderItem;
use Brick\Money\Money;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\Attributes\WithCast;
use Spatie\LaravelData\Attributes\WithTransformer;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Lazy;
use App\Modules\Order\Data\ProductOfferData;

final class OrderItemData extends Data
{
    public function __construct(
        public readonly string $id,
        public readonly string $orderNumber,
        #[MapInputName('price')]
        #[WithTransformer(MoneyTransformer::class)]
        #[WithCast(MoneyCast::class)]
        public readonly Money $unitPrice,
        public readonly int $quantity,
        #[MapInputName('tax_fee')]
        #[WithTransformer(MoneyTransformer::class)]
        #[WithCast(MoneyCast::class)]
        public readonly ?Money $taxFee,
        #[MapInputName('total_price')]
        #[WithTransformer(MoneyTransformer::class)]
        #[WithCast(MoneyCast::class)]
        public readonly Money $totalPrice,
        public readonly OrderItemStatus $status,
        public readonly string $productOfferId,
        public readonly ProductData|Lazy $product,
    ) {}

    public static function fromModel(OrderItem $item): self
    {
        // Get the product offer for this order item to access its unit_of_measure
        $productOffer = $item->productOffer;

        // Get all product offers for this product to build the offers array
        $productOffers = $item->product->productOffers()
            ->with('vendor')
            ->whereNull('deactivated_at')
            ->get();

        $offers = $productOffers->map(function ($offer) {
            return ProductOfferData::from([
                'id' => $offer->id,
                'vendorSku' => $offer->vendor_sku,
                'unit_of_measure' => $offer->unit_of_measure,
                'price' => $offer->price ? Money::ofMinor($offer->price, 'USD') : null,
                'stockStatus' => $offer->stock_status,
                'increments' => $offer->increments ?? 1,
                'vendor' => VendorData::from($offer->vendor),
            ]);
        });

        return self::from([
            'id' => $item->id,
            'orderNumber' => $item->order->order_number,
            'unitPrice' => Money::ofMinor($item->price ?? 0, 'USD'),
            'quantity' => $item->quantity,
            'taxFee' => $item->tax_fee ? Money::ofMinor($item->tax_fee, 'USD') : null,
            'totalPrice' => Money::ofMinor($item->total_price ?? 0, 'USD'),
            'status' => $item->status,
            'productOfferId' => $item->product_offer_id,
            'product' => ProductData::from([
                'id' => $item->product->id,
                'name' => $item->product->name,
                'imageUrl' => $item->product->image_url,
                'hasOffers' => $item->product->hasOffersForClinic($item->order->clinic),
                'unit_of_measure' => $productOffer->unit_of_measure,
                'offers' => $offers,
            ]),
        ]);
    }
}
