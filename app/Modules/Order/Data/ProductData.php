<?php

declare(strict_types=1);

namespace App\Modules\Order\Data;

use Illuminate\Support\Collection;
use Spatie\LaravelData\Attributes\DataCollectionOf;
use Spatie\LaravelData\Data;

final class ProductData extends Data
{
    public function __construct(
        public readonly string $id,
        public readonly string $name,
        public readonly ?string $imageUrl,
        public readonly bool $hasOffers,
        #[DataCollectionOf(ProductOfferData::class)]
        public readonly Collection $offers,
    ) {}
}
